'use client';

import React from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'motion/react';
import { Monitor } from 'lucide-react';
import Dashboard from '@/components/dashboard';

const InteractiveModel = () => {
  const { t } = useTranslation('products');

  return (
    <section className="relative overflow-hidden py-20">
      {/* Gradient background */}
      <div className="from-primary/5 via-secondary/5 to-accent/5 dark:from-primary/10 dark:via-secondary/10 dark:to-accent/10 absolute inset-0 bg-gradient-to-br" />

      {/* Background pattern */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,rgba(255,255,255,0.15)_1px,transparent_0)] [background-size:20px_20px] dark:bg-[radial-gradient(circle_at_1px_1px,rgba(255,255,255,0.05)_1px,transparent_0)]" />
      </div>

      {/* Floating background elements */}
      <div className="pointer-events-none absolute inset-0">
        <motion.div
          animate={{
            x: [0, 100, 0],
            y: [0, -50, 0],
            scale: [1, 1.2, 1],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
          className="from-primary/20 to-secondary/20 absolute top-1/4 left-1/6 h-32 w-32 rounded-full bg-gradient-to-r blur-2xl"
        />

        <motion.div
          animate={{
            x: [0, -80, 0],
            y: [0, 60, 0],
            scale: [1, 0.8, 1],
          }}
          transition={{
            duration: 18,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: 5,
          }}
          className="from-accent/20 to-primary/20 absolute right-1/6 bottom-1/4 h-24 w-24 rounded-full bg-gradient-to-r blur-xl"
        />
      </div>

      <div className="relative z-10 mx-auto max-w-7xl px-4">
        {/* Section header */}
        <div className="mb-16 text-center">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: '-100px' }}
            transition={{ duration: 0.8 }}
            className="text-foreground mb-4 text-3xl font-bold md:text-4xl lg:text-5xl"
          >
            {t('interactive_model.title')}
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: '-100px' }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-muted-foreground text-lg md:text-xl"
          >
            {t('interactive_model.subtitle')}
          </motion.p>
        </div>

        {/* Dashboard */}
        <Dashboard />

        {/* Additional info */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: '-100px' }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="mt-16 text-center"
        >
          <div className="border-border/50 bg-card/50 mx-auto max-w-2xl rounded-2xl border p-8 backdrop-blur-sm">
            <div className="mb-4 flex justify-center">
              <Monitor className="text-primary h-12 w-12" />
            </div>
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              Experience the Full System
            </h3>
            <p className="text-muted-foreground">
              Each module integrates seamlessly with others, creating a unified
              document management ecosystem that adapts to your business needs.
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default InteractiveModel;
