'use client';

import React from 'react';
import { motion } from 'motion/react';
import { useTranslation, Trans } from 'react-i18next';
import { Lightbulb, Users, Target, TrendingUp, Scale, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

const CompanyCulture = () => {
  const { t } = useTranslation('careers');

  const values = [
    {
      key: 'innovation',
      icon: Lightbulb,
      color: 'from-primary to-secondary',
      bgColor: 'bg-primary/10',
    },
    {
      key: 'collaboration',
      icon: Users,
      color: 'from-secondary to-accent',
      bgColor: 'bg-secondary/10',
    },
    {
      key: 'excellence',
      icon: Target,
      color: 'from-accent to-primary',
      bgColor: 'bg-accent/10',
    },
    {
      key: 'growth',
      icon: TrendingUp,
      color: 'from-primary to-accent',
      bgColor: 'bg-primary/10',
    },
    {
      key: 'balance',
      icon: Scale,
      color: 'from-secondary to-primary',
      bgColor: 'bg-secondary/10',
    },
    {
      key: 'impact',
      icon: Zap,
      color: 'from-accent to-secondary',
      bgColor: 'bg-accent/10',
    },
  ];

  return (
    <section id="company-culture" className="relative px-4 py-20 lg:py-32">
      {/* Background Elements */}
      <div className="absolute inset-0 -z-10">
        <div className="from-secondary/5 to-accent/5 absolute top-1/3 left-1/3 h-72 w-72 rounded-full bg-gradient-to-r blur-3xl" />
        <div className="from-primary/5 to-secondary/5 absolute right-1/3 bottom-1/3 h-72 w-72 rounded-full bg-gradient-to-r blur-3xl" />
      </div>

      <div className="container mx-auto max-w-7xl">
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true, margin: '-100px' }}
          transition={{
            staggerChildren: 0.1,
            delayChildren: 0.2,
          }}
          className="space-y-16"
        >
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{
              duration: 0.6,
              ease: [0.25, 0.46, 0.45, 0.94],
            }}
            className="text-center"
          >
            <div className="space-y-6">
              <motion.h2
                className="text-3xl font-bold md:text-4xl lg:text-5xl"
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.2 }}
              >
                <Trans
                  i18nKey="culture.title"
                  ns="careers"
                  components={{
                    highlighted: (
                      <span className="from-primary via-secondary to-accent bg-gradient-to-r bg-clip-text text-transparent" />
                    ),
                  }}
                />
              </motion.h2>

              <motion.p
                className="text-muted-foreground mx-auto max-w-2xl text-xl leading-relaxed"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.3, duration: 0.6 }}
              >
                {t('culture.subtitle')}
              </motion.p>

              <motion.p
                className="text-muted-foreground mx-auto max-w-3xl leading-relaxed"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.4, duration: 0.6 }}
              >
                {t('culture.description')}
              </motion.p>
            </div>
          </motion.div>

          {/* Values Grid */}
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{
              staggerChildren: 0.1,
              delayChildren: 0.2,
            }}
            className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3"
          >
            {values.map((value, index) => {
              const IconComponent = value.icon;
              return (
                <motion.div
                  key={value.key}
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{
                    duration: 0.6,
                    ease: [0.25, 0.46, 0.45, 0.94],
                    delay: index * 0.1,
                  }}
                  whileHover={{
                    y: -8,
                    scale: 1.02,
                    rotateY: 5,
                    transition: { duration: 0.3, ease: 'easeOut' },
                  }}
                  className="group h-full"
                >
                  <Card className="bg-card/80 border-border/50 group-hover:border-primary/30 h-full backdrop-blur-sm transition-all duration-300 hover:shadow-xl">
                    <CardContent className="space-y-4 p-6 text-center">
                      {/* Icon */}
                      <div className="flex justify-center">
                        <div
                          className={`flex h-16 w-16 items-center justify-center rounded-full ${value.bgColor} transition-all duration-300 group-hover:scale-110`}
                        >
                          <div
                            className={`flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-r ${value.color} shadow-lg`}
                          >
                            <IconComponent className="h-6 w-6 text-white" />
                          </div>
                        </div>
                      </div>

                      {/* Content */}
                      <div className="space-y-3">
                        <h3 className="text-foreground text-xl font-semibold">
                          {t(`culture.values.${value.key}.title`)}
                        </h3>
                        <p className="text-muted-foreground leading-relaxed">
                          {t(`culture.values.${value.key}.description`)}
                        </p>
                      </div>

                      {/* Hover Effect */}
                      <motion.div
                        className={`mx-auto h-1 w-0 rounded-full bg-gradient-to-r ${value.color} transition-all duration-300 group-hover:w-16`}
                        initial={{ width: 0 }}
                        whileHover={{ width: 64 }}
                      />
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </motion.div>

          {/* Bottom CTA */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{
              duration: 0.6,
              ease: [0.25, 0.46, 0.45, 0.94],
            }}
            className="text-center"
          >
            <Card className="from-primary/5 via-secondary/5 to-accent/5 border-primary/20 mx-auto max-w-2xl bg-gradient-to-r backdrop-blur-sm">
              <CardContent className="space-y-4 p-8">
                <div className="space-y-2">
                  <h3 className="text-foreground text-2xl font-bold">
                    Ready to be part of our culture?
                  </h3>
                  <p className="text-muted-foreground">
                    Join a team that values innovation, collaboration, and
                    making a meaningful impact.
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default CompanyCulture;
