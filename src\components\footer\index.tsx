'use client';

import React from 'react';
import { motion } from 'motion/react';
import { containerVariants } from '@/constants/footer';

import System from './system';
import Products from './products';
import About from './about';
import Newsletter from './newsletter';
import Copyright from './copyright';
import Decorative from './decorative';

const Footer = () => {
  return (
    <motion.footer
      className="footer-container text-foreground py-16 transition-all duration-700 ease-out"
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: '-100px' }}
      variants={containerVariants}
    >
      <div className="container mx-auto max-w-7xl px-6">
        <motion.div
          className="grid grid-cols-1 gap-8 sm:grid-cols-2 sm:gap-6 lg:grid-cols-12 lg:gap-8"
          variants={containerVariants}
        >
          <div className="sm:col-span-1 lg:col-span-3">
            <System />
          </div>

          <div className="sm:col-span-1 lg:col-span-2">
            <Products />
          </div>

          <div className="sm:col-span-1 lg:col-span-2">
            <About />
          </div>

          <div className="sm:col-span-1 lg:col-span-5">
            <Newsletter />
          </div>
        </motion.div>

        <div className="mt-12">
          <Copyright />
        </div>

        <Decorative />
      </div>
    </motion.footer>
  );
};

export default Footer;
