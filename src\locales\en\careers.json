{"site": {"title": "Careers - Join Our Team", "description": "Join <PERSON> and be part of a team that's revolutionizing document processing. Explore career opportunities, learn about our culture, and discover the benefits of working with us."}, "hero": {"title": "Shape the Future of <highlighted>Document Processing</highlighted>", "subtitle": "Join our innovative team and help transform how businesses handle their most critical documents", "description": "At Centris, we're not just building software – we're creating the future of intelligent document processing. Join us in revolutionizing how organizations manage, process, and extract value from their documents.", "cta": {"primary": "View Open Positions", "secondary": "Learn About Our Culture"}, "stats": {"employees": "50+", "employees_label": "Team Members", "countries": "10+", "countries_label": "Countries Served", "satisfaction": "95%", "satisfaction_label": "Employee Satisfaction"}}, "culture": {"title": "Our <highlighted>Culture</highlighted> & Values", "subtitle": "What makes <PERSON><PERSON><PERSON> a great place to work", "description": "We believe that great products come from great teams. Our culture is built on collaboration, innovation, and a shared commitment to excellence.", "values": {"innovation": {"title": "Innovation First", "description": "We encourage creative thinking and embrace new technologies to solve complex challenges."}, "collaboration": {"title": "Team Collaboration", "description": "We work together across departments and time zones to achieve our common goals."}, "excellence": {"title": "Pursuit of Excellence", "description": "We strive for the highest quality in everything we do, from code to customer service."}, "growth": {"title": "Continuous Growth", "description": "We invest in our people's development and provide opportunities for career advancement."}, "balance": {"title": "Work-Life Balance", "description": "We believe in maintaining a healthy balance between professional and personal life."}, "impact": {"title": "Meaningful Impact", "description": "Every role at Centris contributes to transforming how businesses operate worldwide."}}}, "benefits": {"title": "Benefits & <highlighted>Perks</highlighted>", "subtitle": "We take care of our team members", "description": "Comprehensive benefits package designed to support your health, growth, and well-being.", "categories": {"health": {"title": "Health & Wellness", "items": {"medical": {"title": "Medical Insurance", "description": "Comprehensive health coverage for you and your family"}, "dental": {"title": "Dental & Vision", "description": "Complete dental and vision care coverage"}, "wellness": {"title": "Wellness Programs", "description": "Gym memberships, mental health support, and wellness initiatives"}}}, "financial": {"title": "Financial Benefits", "items": {"salary": {"title": "Competitive Salary", "description": "Market-leading compensation packages"}, "bonus": {"title": "Performance Bonuses", "description": "Annual bonuses based on individual and company performance"}, "equity": {"title": "Equity Options", "description": "Share in the company's success with stock options"}}}, "time": {"title": "Time & Flexibility", "items": {"pto": {"title": "Unlimited PTO", "description": "Take the time you need to recharge and maintain work-life balance"}, "remote": {"title": "Remote Work", "description": "Flexible work arrangements including remote and hybrid options"}, "hours": {"title": "Flexible Hours", "description": "Work when you're most productive with flexible scheduling"}}}, "development": {"title": "Growth & Development", "items": {"learning": {"title": "Learning Budget", "description": "Annual budget for courses, conferences, and professional development"}, "mentorship": {"title": "Mentorship Programs", "description": "Guidance from senior team members and industry experts"}, "conferences": {"title": "Conference Attendance", "description": "Opportunities to attend industry conferences and events"}}}}}, "positions": {"title": "Open <highlighted>Positions</highlighted>", "subtitle": "Find your next career opportunity", "description": "We're always looking for talented individuals to join our team. Explore our current openings and find the perfect role for you.", "filters": {"all": "All Departments", "engineering": "Engineering", "product": "Product", "design": "Design", "sales": "Sales", "marketing": "Marketing", "support": "Customer Support"}, "search_placeholder": "Search positions...", "no_results": "No positions found matching your criteria.", "jobs": {"senior_frontend": {"title": "Senior Frontend Developer", "department": "Engineering", "location": "Remote / Prague", "type": "Full-time", "description": "Join our frontend team to build beautiful, responsive user interfaces for our document processing platform.", "requirements": ["5+ years of React/TypeScript experience", "Experience with modern CSS frameworks", "Knowledge of state management libraries", "Strong understanding of web performance"]}, "backend_engineer": {"title": "Backend Engineer", "department": "Engineering", "location": "Remote / Prague", "type": "Full-time", "description": "Build scalable backend systems that power our document processing capabilities.", "requirements": ["3+ years of backend development experience", "Proficiency in Python or Node.js", "Experience with cloud platforms (AWS/Azure)", "Knowledge of microservices architecture"]}, "product_manager": {"title": "Product Manager", "department": "Product", "location": "Prague", "type": "Full-time", "description": "Drive product strategy and roadmap for our document processing solutions.", "requirements": ["3+ years of product management experience", "Experience with B2B SaaS products", "Strong analytical and communication skills", "Understanding of enterprise software"]}}}, "process": {"title": "Application <highlighted>Process</highlighted>", "subtitle": "Your journey to joining Centris", "description": "We've designed our hiring process to be transparent, efficient, and focused on finding the right fit for both you and our team.", "steps": {"apply": {"title": "Apply Online", "description": "Submit your application through our careers page or reach out directly to our team."}, "review": {"title": "Application Review", "description": "Our hiring team will review your application and get back to you within 3-5 business days."}, "interview": {"title": "Initial Interview", "description": "A conversation with our hiring manager to discuss your background and the role."}, "technical": {"title": "Technical Assessment", "description": "For technical roles, a practical assessment or technical discussion with the team."}, "final": {"title": "Final Interview", "description": "Meet with team members and leadership to ensure mutual fit and discuss next steps."}, "offer": {"title": "Offer & Onboarding", "description": "Receive your offer and begin the onboarding process to join the <PERSON><PERSON><PERSON> family."}}}, "contact": {"title": "Ready to Join Our Team?", "subtitle": "Start your journey with <PERSON><PERSON><PERSON> today", "description": "Don't see a position that fits? We're always interested in hearing from talented individuals. Reach out and let's start a conversation.", "cta": {"apply": "Apply Now", "contact": "Contact HR Team", "email": "<EMAIL>"}, "or": "or", "general_inquiry": "Send us a general inquiry"}}