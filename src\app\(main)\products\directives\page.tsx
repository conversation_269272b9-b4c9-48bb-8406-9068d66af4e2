import React from 'react';
import type { Metadata } from 'next';
import { detectLanguageFromHeaders } from '@/lib/i18n/detector';
import { getServerTranslation } from '@/lib/i18n/server';

export async function generateMetadata(): Promise<Metadata> {
  const language = await detectLanguageFromHeaders();
  const { t } = await getServerTranslation(language, 'directives');

  return {
    title: t('site.title'),
    description: t('site.description'),
  };
}

const DirectivesPage = () => {
  return <div>DirectivesPage</div>;
};

export default DirectivesPage;
