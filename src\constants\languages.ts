/**
 * Available languages in the application
 */
export const LANGUAGES = ['en', 'cs'] as const;

/**
 * Available namespaces in the application
 */
export const LANGUAGE_NAMESPACES = [
  'common',
  'products',
  'contact',
  'about',
  'faq',
  'careers',
  'mailroom',
  'received-invoices',
  'issued-invoices',
  'contracts',
  'requisitions',
  'directives',
  'digital-archive',
  'identity-management',
] as const;

/**
 * Type for supported languages
 */
export type Language = (typeof LANGUAGES)[number];

/**
 * Metadata for individual languages
 * Contains localization and formatting information for each supported language
 */
export const LANGUAGE_METADATA = {
  en: {
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸',
    dir: 'ltr' as const,
    dateFormat: 'MM/dd/yyyy',
    numberFormat: 'en-US',
  },
  cs: {
    name: 'Czech',
    nativeName: 'Čeština',
    flag: '🇨🇿',
    dir: 'ltr' as const,
    dateFormat: 'dd.MM.yyyy',
    numberFormat: 'cs-CZ',
  },
} as const satisfies Record<
  Language,
  {
    name: string;
    nativeName: string;
    flag: string;
    dir: 'ltr' | 'rtl';
    dateFormat: string;
    numberFormat: string;
  }
>;

/**
 * Default language for the application
 */
export const DEFAULT_LANGUAGE: Language = 'en';

/**
 * Helper function to get language metadata
 */
export const getLanguageMetadata = (lang: Language) => LANGUAGE_METADATA[lang];

/**
 * Helper function to check if language is supported
 */
export const isSupportedLanguage = (lang: string): lang is Language =>
  LANGUAGES.includes(lang as Language);
