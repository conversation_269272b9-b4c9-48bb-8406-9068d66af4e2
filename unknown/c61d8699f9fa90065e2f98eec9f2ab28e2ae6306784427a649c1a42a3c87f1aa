'use client';

import React, { useRef, useEffect, useState } from 'react';
import { motion } from 'motion/react';
import { statsData } from '@/constants/stats';
import AnimatedCounter from '@/components/animated-counter';
import GradientHighlighter from '@/components/gradient-highlighter';

const Stats: React.FC = () => {
  const containerRef = useRef<HTMLElement>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        setMousePosition({
          x: e.clientX - rect.left,
          y: e.clientY - rect.top,
        });
      }
    };

    const container = containerRef.current;
    if (container) {
      container.addEventListener('mousemove', handleMouseMove);
      return () => container.removeEventListener('mousemove', handleMouseMove);
    }
  }, []);

  return (
    <section ref={containerRef} className="relative overflow-hidden py-24">
      {/* Animated background with noise texture */}
      <div className="from-background via-muted/30 to-background dark:from-background dark:via-muted/20 dark:to-card absolute inset-0 bg-gradient-to-br">
        <div
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
            backgroundSize: '256px 256px',
          }}
        />
      </div>

      {/* Floating background particles */}
      <div className="pointer-events-none absolute inset-0">
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="bg-primary/20 absolute h-1 w-1 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -20, 0],
              opacity: [0.2, 0.8, 0.2],
            }}
            transition={{
              duration: 3 + Math.random() * 4,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      <div className="relative z-10 container mx-auto px-6">
        {/* Section header with kinetic typography */}
        <motion.div
          className="mb-16 text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.h2
            className="from-foreground to-muted-foreground mb-6 bg-gradient-to-r bg-clip-text text-4xl leading-[1.15] font-bold text-transparent text-shadow-2xs md:text-5xl lg:text-6xl"
            whileInView={{
              backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],
            }}
            transition={{ duration: 4, repeat: Infinity }}
            style={{ backgroundSize: '200% 200%' }}
          >
            Proven <GradientHighlighter>Excellence</GradientHighlighter>
          </motion.h2>
          <motion.p
            className="text-shadow-xstext-muted-foreground mx-auto max-w-4xl text-xl leading-relaxed"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.8 }}
          >
            Transforming document processing with cutting-edge technology and
            unmatched precision
          </motion.p>
        </motion.div>

        {/* Stats grid with enhanced cards */}
        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
          {statsData.map((stat, index) => (
            <motion.div
              key={index}
              className="group relative"
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{
                duration: 0.6,
                delay: index * 0.1,
                type: 'spring',
                stiffness: 100,
              }}
              viewport={{ once: true }}
              whileHover={{
                y: -10,
                transition: { duration: 0.3 },
              }}
            >
              {/* Card with glassmorphism effect */}
              <div className="relative overflow-hidden rounded-3xl border border-white/20 bg-white/10 p-8 shadow-2xl backdrop-blur-xl dark:border-gray-700/30 dark:bg-gray-900/20">
                {/* Dynamic background gradient */}
                <motion.div
                  className={`absolute inset-0 bg-gradient-to-br ${stat.color} opacity-5 transition-opacity duration-500 group-hover:opacity-10`}
                  animate={{
                    scale: [1, 1.05, 1],
                    rotate: [0, 1, 0],
                  }}
                  transition={{
                    duration: 6,
                    repeat: Infinity,
                    ease: 'easeInOut',
                  }}
                />

                {/* Floating icon with 3D effect */}
                <motion.div
                  className="relative z-10 mb-4 text-4xl"
                  whileHover={{
                    rotateY: 180,
                    scale: 1.2,
                  }}
                  transition={{ duration: 0.6 }}
                  style={{
                    transformStyle: 'preserve-3d',
                    filter: `drop-shadow(0 10px 20px ${stat.shadowColor})`,
                  }}
                >
                  {stat.icon}
                </motion.div>

                {/* Counter with enhanced styling */}
                <div className="relative z-10 mb-3">
                  <div className="text-4xl leading-none font-bold md:text-5xl">
                    <AnimatedCounter
                      end={stat.value}
                      suffix={stat.suffix}
                      duration={2000 + index * 200}
                    />
                  </div>
                </div>

                {/* Label with hover effect */}
                <motion.p
                  className="text-muted-foreground relative z-10 text-sm font-medium"
                  whileHover={{
                    scale: 1.05,
                    color: 'var(--color-foreground)',
                  }}
                  transition={{ duration: 0.2 }}
                >
                  {stat.label}
                </motion.p>

                {/* Animated border gradient */}
                <motion.div
                  className="absolute inset-0 rounded-3xl"
                  style={{
                    background: `linear-gradient(45deg, transparent, ${stat.shadowColor}, transparent)`,
                    padding: '2px',
                  }}
                  initial={{ opacity: 0 }}
                  whileHover={{ opacity: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  <div className="bg-background/50 dark:bg-card/50 h-full w-full rounded-3xl backdrop-blur-xl" />
                </motion.div>

                {/* Interactive light effect following mouse */}
                <motion.div
                  className="pointer-events-none absolute h-32 w-32 rounded-full"
                  style={{
                    background: `radial-gradient(circle, ${stat.shadowColor} 0%, transparent 70%)`,
                    left: mousePosition.x - 64,
                    top: mousePosition.y - 64,
                  }}
                  initial={{ opacity: 0 }}
                  whileHover={{ opacity: 0.6 }}
                  transition={{ duration: 0.2 }}
                />
              </div>

              {/* Floating particles around card */}
              <div className="pointer-events-none absolute inset-0">
                {[...Array(3)].map((_, i) => (
                  <motion.div
                    key={i}
                    className="bg-primary/40 absolute h-1 w-1 rounded-full"
                    style={{
                      left: `${20 + Math.random() * 60}%`,
                      top: `${20 + Math.random() * 60}%`,
                    }}
                    animate={{
                      scale: [0, 1, 0],
                      opacity: [0, 1, 0],
                    }}
                    transition={{
                      duration: 2 + Math.random() * 2,
                      repeat: Infinity,
                      delay: index * 0.2 + Math.random(),
                    }}
                  />
                ))}
              </div>
            </motion.div>
          ))}
        </div>

        {/* Bottom accent line */}
        <motion.div
          className="mt-16 flex justify-center"
          initial={{ opacity: 0, scaleX: 0 }}
          whileInView={{ opacity: 1, scaleX: 1 }}
          transition={{ delay: 1, duration: 1 }}
          viewport={{ once: true }}
        >
          <div className="from-primary via-secondary to-accent h-1 w-64 rounded-full bg-gradient-to-r shadow-lg" />
        </motion.div>
      </div>
    </section>
  );
};

export default Stats;
