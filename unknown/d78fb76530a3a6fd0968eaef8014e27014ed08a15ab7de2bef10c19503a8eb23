'use client';

import React from 'react';
import { motion } from 'motion/react';
import CareersHero from './hero';
import CompanyCulture from './culture';
import Benefits from './benefits';
import JobListings from './job-listings';
import ApplicationProcess from './application-process';
import ContactCTA from './contact-cta';

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2,
    },
  },
};

const Careers = () => {
  return (
    <div className="from-background via-muted/30 to-background min-h-screen bg-gradient-to-br">
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="space-y-0"
      >
        {/* Hero Section */}
        <CareersHero />

        {/* Company Culture Section */}
        <CompanyCulture />

        {/* Benefits Section */}
        <Benefits />

        {/* Job Listings Section */}
        <JobListings />

        {/* Application Process Section */}
        <ApplicationProcess />

        {/* Contact CTA Section */}
        <ContactCTA />
      </motion.div>
    </div>
  );
};

export default Careers;
