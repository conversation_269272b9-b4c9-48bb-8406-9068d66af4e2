'use client';

import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'motion/react';
import { toast } from 'sonner';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  createNewsletterSchema,
  NewsletterFormData,
} from '@/schema/newsletter';
import { Mail, Loader2, MessageCircle } from 'lucide-react';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { itemVariants } from '@/constants/footer';

const Newsletter = () => {
  const { t } = useTranslation('common');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Create schema with current translations
  const newsletterSchema = createNewsletterSchema(t);

  const form = useForm<NewsletterFormData>({
    resolver: zodResolver(newsletterSchema),
    defaultValues: {
      email: '',
    },
  });

  const onSubmit = async (data: NewsletterFormData) => {
    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise((resolve) => {
        console.log('Newsletter subscription:', data);
        setTimeout(resolve, 2000);
      });

      // Show success toast
      toast.success(t('footer.newsletter.form.success.title'), {
        description: t('footer.newsletter.form.success.description'),
      });

      // Reset form
      form.reset();
    } catch (error) {
      // Show error toast
      toast.error(t('footer.newsletter.form.error.title'), {
        description: t('footer.newsletter.form.error.description'),
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleContactClick = () => {
    // Navigate to contact page or scroll to contact section
    window.location.href = '/contact';
  };

  return (
    <motion.div className="newsletter-gradient w-full" variants={itemVariants}>
      <div className="newsletter-container space-y-6">
        <motion.h4
          className="footer-gradient-text text-lg font-semibold"
          whileHover={{ scale: 1.02 }}
          transition={{ duration: 0.2 }}
        >
          {t('footer.newsletter.title')}
        </motion.h4>

        <motion.p className="text-muted-foreground text-sm leading-relaxed">
          {t('footer.newsletter.description')}
        </motion.p>

        <motion.div className="space-y-4">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              {/* Email Input Field - Vylepšený dark mode support */}
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <motion.div
                        whileFocus={{
                          scale: 1.01,
                          transition: { duration: 0.2 },
                        }}
                        whileHover={{
                          scale: 1.005,
                          transition: { duration: 0.2 },
                        }}
                      >
                        <Input
                          type="email"
                          placeholder={t(
                            'footer.newsletter.form.email.placeholder'
                          )}
                          className="h-12 w-full rounded-xl border-2 border-gray-200/70 bg-white/95 px-4 py-3 text-base text-gray-900 backdrop-blur-sm transition-all duration-300 ease-out placeholder:text-gray-500 hover:border-gray-300 hover:shadow-md hover:shadow-gray-200/20 focus:border-blue-600 focus:shadow-lg focus:ring-4 focus:shadow-blue-500/10 focus:ring-blue-500/30 focus:outline-none sm:text-sm dark:border-gray-600/70 dark:bg-gray-800/95 dark:text-gray-100 dark:placeholder:text-gray-400 dark:hover:border-gray-500/80 dark:hover:shadow-gray-700/30 dark:focus:border-cyan-400 dark:focus:shadow-cyan-400/10 dark:focus:ring-cyan-400/30"
                          {...field}
                        />
                      </motion.div>
                    </FormControl>
                    <FormMessage className="mt-1 text-xs" />
                  </FormItem>
                )}
              />

              {/* Subscribe Button - Modernizované hover efekty */}
              <motion.div
                whileHover={{
                  scale: 1.02,
                  transition: { duration: 0.2 },
                }}
                whileTap={{ scale: 0.98 }}
              >
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="h-12 w-full rounded-xl border-0 bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-3 text-base font-medium text-white shadow-lg shadow-blue-500/25 backdrop-blur-sm transition-all duration-300 ease-out hover:from-blue-700 hover:to-blue-800 hover:shadow-xl hover:shadow-blue-500/40 focus:ring-4 focus:ring-blue-500/50 focus:outline-none active:scale-95 disabled:cursor-not-allowed disabled:opacity-60 disabled:hover:scale-100 disabled:hover:shadow-lg sm:text-sm dark:from-cyan-500 dark:to-blue-600 dark:text-gray-900 dark:shadow-cyan-400/25 dark:hover:from-cyan-400 dark:hover:to-blue-500 dark:hover:shadow-cyan-400/40 dark:focus:ring-cyan-400/50"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 flex-shrink-0 animate-spin" />
                      <span className="truncate">
                        {t('footer.newsletter.form.subscribing')}
                      </span>
                    </>
                  ) : (
                    <>
                      <Mail className="mr-2 h-4 w-4 flex-shrink-0" />
                      <span className="truncate">
                        {t('footer.newsletter.form.subscribe')}
                      </span>
                    </>
                  )}
                </Button>
              </motion.div>
            </form>
          </Form>

          {/* Contact Button - Vylepšené dark mode hover efekty */}
          <motion.div
            whileHover={{
              scale: 1.02,
              transition: { duration: 0.2 },
            }}
            whileTap={{ scale: 0.98 }}
          >
            <Button
              onClick={handleContactClick}
              variant="outline"
              className="h-12 w-full rounded-xl border-2 border-gray-300/70 bg-white/80 px-6 py-3 text-base font-medium text-gray-700 shadow-md shadow-gray-200/30 backdrop-blur-sm transition-all duration-300 ease-out hover:border-gray-400 hover:bg-gray-50 hover:text-gray-900 hover:shadow-lg hover:shadow-gray-300/40 focus:ring-4 focus:ring-gray-400/30 focus:outline-none active:scale-95 sm:text-sm dark:border-gray-600/70 dark:bg-gray-800/80 dark:text-gray-200 dark:shadow-gray-800/30 dark:hover:border-gray-500 dark:hover:bg-gray-700/90 dark:hover:text-white dark:hover:shadow-gray-700/40 dark:focus:ring-gray-500/30"
            >
              <MessageCircle className="mr-2 h-4 w-4 flex-shrink-0" />
              <span className="truncate">
                {t('footer.newsletter.form.contact')}
              </span>
            </Button>
          </motion.div>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default Newsletter;
