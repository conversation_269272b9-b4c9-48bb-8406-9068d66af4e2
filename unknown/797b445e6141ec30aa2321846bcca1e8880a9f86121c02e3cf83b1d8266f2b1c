'use client';

import React from 'react';
import { motion } from 'motion/react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'lucide-react';
import { DashboardModules } from '@/constants/dashboard';

const Dashboard = () => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.8, delay: 0.4 }}
      className="relative mx-auto max-w-4xl"
    >
      {/* Dashboard mockup container */}
      <div className="border-border/50 bg-card/80 relative rounded-3xl border p-8 shadow-2xl backdrop-blur-sm">
        {/* Dashboard header */}
        <div className="mb-8 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="h-3 w-3 rounded-full bg-red-400"></div>
            <div className="h-3 w-3 rounded-full bg-yellow-400"></div>
            <div className="h-3 w-3 rounded-full bg-green-400"></div>
          </div>
          <div className="text-muted-foreground text-sm">Centris Dashboard</div>
        </div>

        {/* Interactive grid of modules */}
        <div className="grid grid-cols-2 gap-6 md:grid-cols-3 lg:grid-cols-4">
          {DashboardModules.map((item, index) => (
            <motion.div
              key={item.label}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}
              whileHover={{
                scale: 1.05,
                y: -5,
                transition: { duration: 0.2 },
              }}
              className="group relative cursor-pointer"
            >
              <div
                className={`rounded-2xl bg-gradient-to-br ${item.color} p-6 shadow-lg transition-all duration-300 group-hover:shadow-xl`}
              >
                <div className="mb-3 text-center text-3xl">{item.icon}</div>
                <div className="text-center text-sm font-medium text-white">
                  {item.label}
                </div>

                {/* Hover effect overlay */}
                <motion.div
                  initial={{ opacity: 0 }}
                  whileHover={{ opacity: 1 }}
                  className="absolute inset-0 rounded-2xl bg-white/20 backdrop-blur-sm"
                />

                {/* Click indicator */}
                <motion.div
                  initial={{ scale: 0, opacity: 0 }}
                  whileHover={{ scale: 1, opacity: 1 }}
                  className="absolute -top-2 -right-2 rounded-full bg-white p-1 shadow-lg"
                >
                  <MousePointer className="h-4 w-4 text-gray-600" />
                </motion.div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Floating interaction hints */}
        <motion.div
          animate={{
            y: [0, -10, 0],
            opacity: [0.5, 1, 0.5],
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
          className="bg-primary absolute -top-4 left-1/2 -translate-x-1/2 rounded-full px-4 py-2 text-sm text-white shadow-lg"
        >
          <div className="flex items-center gap-2">
            <Sparkles className="h-4 w-4" />
            <span>Interactive</span>
          </div>
        </motion.div>
      </div>

      {/* Decorative elements around the dashboard */}
      <motion.div
        animate={{
          rotate: [0, 360],
        }}
        transition={{
          duration: 20,
          repeat: Infinity,
          ease: 'linear',
        }}
        className="from-primary/20 to-secondary/20 absolute -top-8 -left-8 h-16 w-16 rounded-full bg-gradient-to-br blur-xl"
      />

      <motion.div
        animate={{
          rotate: [360, 0],
        }}
        transition={{
          duration: 25,
          repeat: Infinity,
          ease: 'linear',
        }}
        className="from-accent/20 to-primary/20 absolute -right-8 -bottom-8 h-20 w-20 rounded-full bg-gradient-to-br blur-xl"
      />
    </motion.div>
  );
};

export default Dashboard;
