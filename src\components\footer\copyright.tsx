import React from 'react';
import { motion } from 'motion/react';
import { useTranslation } from 'react-i18next';
import {
  containerVariants,
  itemVariants,
  footerLegalLinks,
  getLinkAttributes,
} from '@/constants/footer';
import { currentYear } from '@/utils/helper';

const Copyright = () => {
  const { t } = useTranslation('common');

  return (
    <motion.div
      className="border-border/50 mt-16 border-t pt-8"
      variants={itemVariants}
    >
      <motion.div
        className="flex flex-col items-center justify-between space-y-4 md:flex-row md:space-y-0"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ delay: 0.8, duration: 0.6 }}
        viewport={{ once: true }}
      >
        <motion.p
          className="text-muted-foreground text-sm"
          whileHover={{
            color: 'hsl(var(--foreground))',
            transition: { duration: 0.2 },
          }}
        >
          {t('footer.copyright', { year: currentYear })}
        </motion.p>

        <motion.div
          className="flex space-x-6 text-sm"
          variants={containerVariants}
        >
          {footerLegalLinks.map((link) => {
            const linkAttributes = getLinkAttributes(link);
            return (
              <motion.a
                href={link.href}
                key={link.key}
                {...linkAttributes}
                className="footer-link"
                whileHover={{ x: 4 }}
                whileTap={{ scale: 0.98 }}
                transition={{ duration: 0.2 }}
                aria-label={t(`footer.legal.${link.key}`)}
              >
                {t(`footer.legal.${link.key}`)}
              </motion.a>
            );
          })}
        </motion.div>
      </motion.div>
    </motion.div>
  );
};

export default Copyright;
