'use client';

import React from 'react';
import { motion } from 'motion/react';
import { useTranslation, Trans } from 'react-i18next';
import {
  Send,
  FileText,
  MessageCircle,
  Code,
  Users,
  CheckCircle,
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

const ApplicationProcess = () => {
  const { t } = useTranslation('careers');

  const steps = [
    {
      key: 'apply',
      icon: Send,
      color: 'from-blue-500 to-cyan-500',
      bgColor: 'bg-blue-50 dark:bg-blue-950/20',
    },
    {
      key: 'review',
      icon: FileText,
      color: 'from-green-500 to-emerald-500',
      bgColor: 'bg-green-50 dark:bg-green-950/20',
    },
    {
      key: 'interview',
      icon: MessageCircle,
      color: 'from-purple-500 to-indigo-500',
      bgColor: 'bg-purple-50 dark:bg-purple-950/20',
    },
    {
      key: 'technical',
      icon: Code,
      color: 'from-orange-500 to-red-500',
      bgColor: 'bg-orange-50 dark:bg-orange-950/20',
    },
    {
      key: 'final',
      icon: Users,
      color: 'from-pink-500 to-rose-500',
      bgColor: 'bg-pink-50 dark:bg-pink-950/20',
    },
    {
      key: 'offer',
      icon: CheckCircle,
      color: 'from-emerald-500 to-teal-500',
      bgColor: 'bg-emerald-50 dark:bg-emerald-950/20',
    },
  ];

  return (
    <section className="relative px-4 py-20 lg:py-32">
      {/* Background Elements */}
      <div className="absolute inset-0 -z-10">
        <div className="from-primary/5 to-secondary/5 absolute top-1/4 left-1/3 h-96 w-96 rounded-full bg-gradient-to-r blur-3xl" />
        <div className="from-secondary/5 to-accent/5 absolute right-1/3 bottom-1/4 h-96 w-96 rounded-full bg-gradient-to-r blur-3xl" />
      </div>

      <div className="container mx-auto max-w-6xl">
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true, margin: '-100px' }}
          transition={{
            staggerChildren: 0.1,
            delayChildren: 0.2,
          }}
          className="space-y-16"
        >
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{
              duration: 0.6,
              ease: [0.25, 0.46, 0.45, 0.94],
            }}
            className="text-center"
          >
            <div className="space-y-6">
              <motion.h2
                className="text-3xl font-bold md:text-4xl lg:text-5xl"
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.2 }}
              >
                <Trans
                  i18nKey="process.title"
                  ns="careers"
                  components={{
                    highlighted: (
                      <span className="from-primary via-secondary to-accent bg-gradient-to-r bg-clip-text text-transparent" />
                    ),
                  }}
                />
              </motion.h2>

              <motion.p
                className="text-muted-foreground mx-auto max-w-2xl text-xl leading-relaxed"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.3, duration: 0.6 }}
              >
                {t('process.subtitle')}
              </motion.p>

              <motion.p
                className="text-muted-foreground mx-auto max-w-3xl leading-relaxed"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.4, duration: 0.6 }}
              >
                {t('process.description')}
              </motion.p>
            </div>
          </motion.div>

          {/* Process Steps */}
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{
              staggerChildren: 0.1,
              delayChildren: 0.2,
            }}
            className="relative"
          >
            {/* Timeline Line - Hidden on mobile */}
            <div className="from-primary via-secondary to-accent absolute top-0 left-1/2 hidden h-full w-0.5 -translate-x-1/2 transform bg-gradient-to-b lg:block" />

            <div className="space-y-8 lg:space-y-16">
              {steps.map((step, index) => {
                const StepIcon = step.icon;
                const isEven = index % 2 === 0;

                return (
                  <motion.div
                    key={step.key}
                    initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{
                      duration: 0.6,
                      ease: [0.25, 0.46, 0.45, 0.94],
                      delay: index * 0.1,
                    }}
                    className={`relative flex items-center ${
                      isEven ? 'lg:flex-row' : 'lg:flex-row-reverse'
                    }`}
                  >
                    {/* Step Number Circle - Desktop Timeline */}
                    <div className="bg-background absolute top-1/2 left-1/2 z-10 hidden h-12 w-12 -translate-x-1/2 -translate-y-1/2 transform items-center justify-center rounded-full shadow-lg lg:flex">
                      <div className="from-primary to-secondary flex h-8 w-8 items-center justify-center rounded-full bg-gradient-to-r text-sm font-bold text-white">
                        {index + 1}
                      </div>
                    </div>

                    {/* Content Card */}
                    <motion.div
                      className={`w-full lg:w-5/12 ${
                        isEven ? 'lg:pr-16' : 'lg:pl-16'
                      }`}
                      whileHover={{
                        scale: 1.02,
                        transition: { duration: 0.3, ease: 'easeOut' },
                      }}
                    >
                      <Card className="bg-card/80 border-border/50 group hover:border-primary/30 backdrop-blur-sm transition-all duration-300 hover:shadow-xl">
                        <CardContent className="space-y-4 p-6">
                          {/* Mobile Step Number */}
                          <div className="flex items-center gap-4 lg:hidden">
                            <div className="from-primary to-secondary flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-r text-sm font-bold text-white">
                              {index + 1}
                            </div>
                            <div className="from-primary/20 h-px flex-1 bg-gradient-to-r to-transparent" />
                          </div>

                          {/* Icon and Title */}
                          <div className="flex items-center gap-4">
                            <div
                              className={`flex h-12 w-12 items-center justify-center rounded-full ${step.bgColor} transition-all duration-300 group-hover:scale-110`}
                            >
                              <div
                                className={`flex h-8 w-8 items-center justify-center rounded-full bg-gradient-to-r ${step.color} shadow-lg`}
                              >
                                <StepIcon className="h-4 w-4 text-white" />
                              </div>
                            </div>
                            <h3 className="text-foreground text-xl font-semibold">
                              {t(`process.steps.${step.key}.title`)}
                            </h3>
                          </div>

                          {/* Description */}
                          <p className="text-muted-foreground leading-relaxed">
                            {t(`process.steps.${step.key}.description`)}
                          </p>

                          {/* Progress Indicator */}
                          <motion.div
                            className={`h-1 w-0 rounded-full bg-gradient-to-r ${step.color} transition-all duration-500 group-hover:w-full`}
                            initial={{ width: 0 }}
                            whileInView={{ width: '100%' }}
                            viewport={{ once: true }}
                            transition={{ delay: index * 0.1, duration: 0.8 }}
                          />
                        </CardContent>
                      </Card>
                    </motion.div>

                    {/* Spacer for desktop layout */}
                    <div className="hidden w-5/12 lg:block" />
                  </motion.div>
                );
              })}
            </div>
          </motion.div>

          {/* Bottom CTA */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{
              duration: 0.6,
              ease: [0.25, 0.46, 0.45, 0.94],
            }}
            className="text-center"
          >
            <Card className="from-primary/5 via-secondary/5 to-accent/5 border-primary/20 mx-auto max-w-2xl bg-gradient-to-r backdrop-blur-sm">
              <CardContent className="space-y-4 p-8">
                <div className="space-y-2">
                  <h3 className="text-foreground text-2xl font-bold">
                    Ready to start your journey?
                  </h3>
                  <p className="text-muted-foreground">
                    Our hiring process is designed to be transparent and
                    efficient. We&apos;re excited to learn more about you!
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default ApplicationProcess;
