import React from 'react';
import type { Metadata } from 'next';
import { detectLanguageFromHeaders } from '@/lib/i18n/detector';
import { getServerTranslation } from '@/lib/i18n/server';
import FAQ from '@/components/faq';

export async function generateMetadata(): Promise<Metadata> {
  const language = await detectLanguageFromHeaders();
  const { t } = await getServerTranslation(language, 'faq');

  return {
    title: t('site.title'),
    description: t('site.description'),
    openGraph: {
      title: t('site.title'),
      description: t('site.description'),
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: t('site.title'),
      description: t('site.description'),
    },
  };
}

const FAQPage = () => {
  return <FAQ />;
};

export default FAQPage;
