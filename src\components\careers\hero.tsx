'use client';

import React from 'react';
import { motion } from 'motion/react';
import { useTranslation, Trans } from 'react-i18next';
import { ArrowRight, Users, Globe, Heart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

const CareersHero = () => {
  const { t } = useTranslation('careers');

  const stats = [
    {
      icon: Users,
      value: t('hero.stats.employees'),
      label: t('hero.stats.employees_label'),
      color: 'from-primary to-secondary',
    },
    {
      icon: Globe,
      value: t('hero.stats.countries'),
      label: t('hero.stats.countries_label'),
      color: 'from-secondary to-accent',
    },
    {
      icon: Heart,
      value: t('hero.stats.satisfaction'),
      label: t('hero.stats.satisfaction_label'),
      color: 'from-accent to-primary',
    },
  ];

  const scrollToPositions = () => {
    const element = document.getElementById('job-listings');
    element?.scrollIntoView({ behavior: 'smooth' });
  };

  const scrollToCulture = () => {
    const element = document.getElementById('company-culture');
    element?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <section
      className="relative overflow-hidden px-4 py-20 lg:py-32"
      aria-labelledby="careers-hero-title"
    >
      {/* Background Elements */}
      <div className="absolute inset-0 -z-10">
        <div className="from-primary/5 via-secondary/5 to-accent/5 absolute inset-0 bg-gradient-to-br" />
        <div className="from-primary/10 to-secondary/10 absolute top-1/4 left-1/4 h-96 w-96 rounded-full bg-gradient-to-r blur-3xl" />
        <div className="from-secondary/10 to-accent/10 absolute right-1/4 bottom-1/4 h-96 w-96 rounded-full bg-gradient-to-r blur-3xl" />
      </div>

      <div className="container mx-auto max-w-6xl">
        <div className="grid gap-12 lg:grid-cols-2 lg:gap-16">
          {/* Left Column - Main Content */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{
              duration: 0.6,
              ease: [0.25, 0.46, 0.45, 0.94],
            }}
            className="space-y-8"
          >
            <div className="space-y-6">
              <motion.h1
                id="careers-hero-title"
                className="text-4xl leading-tight font-bold md:text-5xl lg:text-6xl"
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.2 }}
              >
                <Trans
                  i18nKey="hero.title"
                  ns="careers"
                  components={{
                    highlighted: (
                      <span className="from-primary via-secondary to-accent bg-gradient-to-r bg-clip-text text-transparent" />
                    ),
                  }}
                />
              </motion.h1>

              <motion.p
                className="text-muted-foreground text-xl leading-relaxed"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3, duration: 0.6 }}
              >
                {t('hero.subtitle')}
              </motion.p>

              <motion.p
                className="text-muted-foreground leading-relaxed"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4, duration: 0.6 }}
              >
                {t('hero.description')}
              </motion.p>
            </div>

            {/* CTA Buttons */}
            <motion.div
              className="flex flex-col gap-4 sm:flex-row"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.6 }}
            >
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                transition={{ duration: 0.2 }}
              >
                <Button
                  size="lg"
                  onClick={scrollToPositions}
                  className="from-primary to-secondary hover:from-primary/90 hover:to-secondary/90 text-primary-foreground bg-gradient-to-r shadow-lg transition-all duration-300 hover:shadow-xl"
                >
                  {t('hero.cta.primary')}
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                transition={{ duration: 0.2 }}
              >
                <Button
                  variant="outline"
                  size="lg"
                  onClick={scrollToCulture}
                  className="border-border/50 hover:border-primary/50 hover:bg-primary/5 transition-all duration-300"
                >
                  {t('hero.cta.secondary')}
                </Button>
              </motion.div>
            </motion.div>
          </motion.div>

          {/* Right Column - Stats Cards */}
          <motion.div
            className="flex flex-col justify-center space-y-6"
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.6, duration: 0.8 }}
          >
            {stats.map((stat, index) => {
              const IconComponent = stat.icon;
              return (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{
                    duration: 0.5,
                    ease: [0.25, 0.46, 0.45, 0.94],
                    delay: 0.7 + index * 0.1,
                  }}
                  whileHover={{
                    scale: 1.05,
                    rotateY: 5,
                    transition: { duration: 0.3 },
                  }}
                >
                  <Card className="bg-card/80 border-border/50 group backdrop-blur-sm transition-all duration-300 hover:shadow-xl">
                    <CardContent className="flex items-center gap-4 p-6">
                      <div
                        className={`flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-r ${stat.color} shadow-lg transition-all duration-300 group-hover:scale-110`}
                      >
                        <IconComponent className="h-8 w-8 text-white" />
                      </div>
                      <div className="flex-1">
                        <div className="text-foreground text-3xl font-bold">
                          {stat.value}
                        </div>
                        <div className="text-muted-foreground text-sm font-medium">
                          {stat.label}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default CareersHero;
