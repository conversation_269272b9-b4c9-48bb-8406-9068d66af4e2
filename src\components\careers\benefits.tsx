'use client';

import React from 'react';
import { motion } from 'motion/react';
import { useTranslation, Trans } from 'react-i18next';
import {
  Heart,
  DollarSign,
  Clock,
  BookOpen,
  Shield,
  Eye,
  Dumbbell,
  TrendingUp,
  Gift,
  Coins,
  Calendar,
  Home,
  GraduationCap,
  Users,
  Award,
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const Benefits = () => {
  const { t } = useTranslation('careers');

  const benefitCategories = [
    {
      key: 'health',
      icon: Heart,
      color: 'from-red-500 to-pink-500',
      bgColor: 'bg-red-50 dark:bg-red-950/20',
      items: [
        { key: 'medical', icon: Shield },
        { key: 'dental', icon: Eye },
        { key: 'wellness', icon: Dumbbell },
      ],
    },
    {
      key: 'financial',
      icon: DollarSign,
      color: 'from-green-500 to-emerald-500',
      bgColor: 'bg-green-50 dark:bg-green-950/20',
      items: [
        { key: 'salary', icon: TrendingUp },
        { key: 'bonus', icon: Gift },
        { key: 'equity', icon: Coins },
      ],
    },
    {
      key: 'time',
      icon: Clock,
      color: 'from-blue-500 to-cyan-500',
      bgColor: 'bg-blue-50 dark:bg-blue-950/20',
      items: [
        { key: 'pto', icon: Calendar },
        { key: 'remote', icon: Home },
        { key: 'hours', icon: Clock },
      ],
    },
    {
      key: 'development',
      icon: BookOpen,
      color: 'from-purple-500 to-indigo-500',
      bgColor: 'bg-purple-50 dark:bg-purple-950/20',
      items: [
        { key: 'learning', icon: GraduationCap },
        { key: 'mentorship', icon: Users },
        { key: 'conferences', icon: Award },
      ],
    },
  ];

  return (
    <section className="relative px-4 py-20 lg:py-32">
      {/* Background Elements */}
      <div className="absolute inset-0 -z-10">
        <div className="from-primary/5 to-secondary/5 absolute top-1/4 right-1/4 h-96 w-96 rounded-full bg-gradient-to-r blur-3xl" />
        <div className="from-secondary/5 to-accent/5 absolute bottom-1/4 left-1/4 h-96 w-96 rounded-full bg-gradient-to-r blur-3xl" />
      </div>

      <div className="container mx-auto max-w-7xl">
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true, margin: '-100px' }}
          transition={{
            staggerChildren: 0.1,
            delayChildren: 0.2,
          }}
          className="space-y-16"
        >
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{
              duration: 0.6,
              ease: [0.25, 0.46, 0.45, 0.94],
            }}
            className="text-center"
          >
            <div className="space-y-6">
              <motion.h2
                className="text-3xl font-bold md:text-4xl lg:text-5xl"
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.2 }}
              >
                <Trans
                  i18nKey="benefits.title"
                  ns="careers"
                  components={{
                    highlighted: (
                      <span className="from-primary via-secondary to-accent bg-gradient-to-r bg-clip-text text-transparent" />
                    ),
                  }}
                />
              </motion.h2>

              <motion.p
                className="text-muted-foreground mx-auto max-w-2xl text-xl leading-relaxed"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.3, duration: 0.6 }}
              >
                {t('benefits.subtitle')}
              </motion.p>

              <motion.p
                className="text-muted-foreground mx-auto max-w-3xl leading-relaxed"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.4, duration: 0.6 }}
              >
                {t('benefits.description')}
              </motion.p>
            </div>
          </motion.div>

          {/* Benefits Grid */}
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{
              staggerChildren: 0.1,
              delayChildren: 0.2,
            }}
            className="grid gap-8 md:grid-cols-2 lg:grid-cols-4"
          >
            {benefitCategories.map((category, categoryIndex) => {
              const CategoryIcon = category.icon;
              return (
                <motion.div
                  key={category.key}
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{
                    duration: 0.6,
                    ease: [0.25, 0.46, 0.45, 0.94],
                    delay: categoryIndex * 0.1,
                  }}
                  whileHover={{
                    y: -8,
                    scale: 1.02,
                    transition: { duration: 0.3, ease: 'easeOut' },
                  }}
                  className="group h-full"
                >
                  <Card className="bg-card/80 border-border/50 group-hover:border-primary/30 h-full backdrop-blur-sm transition-all duration-300 hover:shadow-xl">
                    <CardHeader className="space-y-4 pb-4">
                      {/* Category Icon */}
                      <div className="flex justify-center">
                        <div
                          className={`flex h-16 w-16 items-center justify-center rounded-full ${category.bgColor} transition-all duration-300 group-hover:scale-110`}
                        >
                          <div
                            className={`flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-r ${category.color} shadow-lg`}
                          >
                            <CategoryIcon className="h-6 w-6 text-white" />
                          </div>
                        </div>
                      </div>

                      {/* Category Title */}
                      <CardTitle className="text-center text-xl">
                        {t(`benefits.categories.${category.key}.title`)}
                      </CardTitle>
                    </CardHeader>

                    <CardContent className="space-y-4 pt-0">
                      {/* Benefit Items */}
                      {category.items.map((item, itemIndex) => {
                        const ItemIcon = item.icon;
                        return (
                          <motion.div
                            key={item.key}
                            className="group/item hover:bg-muted/30 space-y-2 rounded-lg p-3 transition-all duration-200"
                            initial={{ opacity: 0, x: -20 }}
                            whileInView={{ opacity: 1, x: 0 }}
                            viewport={{ once: true }}
                            transition={{
                              delay: categoryIndex * 0.1 + itemIndex * 0.05,
                              duration: 0.4,
                            }}
                          >
                            <div className="flex items-center gap-3">
                              <div className="text-muted-foreground group-hover/item:text-primary flex-shrink-0 transition-colors duration-200">
                                <ItemIcon className="h-4 w-4" />
                              </div>
                              <h4 className="text-foreground font-medium">
                                {t(
                                  `benefits.categories.${category.key}.items.${item.key}.title`
                                )}
                              </h4>
                            </div>
                            <p className="text-muted-foreground ml-7 text-sm leading-relaxed">
                              {t(
                                `benefits.categories.${category.key}.items.${item.key}.description`
                              )}
                            </p>
                          </motion.div>
                        );
                      })}
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Benefits;
