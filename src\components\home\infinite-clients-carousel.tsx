'use client';

import Image from 'next/image';
import { useRef, useState } from 'react';
import { useTranslation, Trans } from 'react-i18next';
import { motion, useAnimationFrame } from 'motion/react';

import { clientLogos } from '@/constants';

// Duplication of logos for infinite carousel
const infiniteLogos = [...clientLogos, ...clientLogos, ...clientLogos];

const InfiniteClientsCarousel = () => {
  const { t } = useTranslation('common');

  const carouselRef = useRef(null);

  const [isPaused, setIsPaused] = useState(false);
  const [scrollX, setScrollX] = useState(0);

  useAnimationFrame(() => {
    if (!isPaused && carouselRef.current) {
      setScrollX((prev) => (prev + 0.5) % (clientLogos.length * 280));
    }
  });

  return (
    <section className="bg-background relative overflow-hidden py-36">
      {/* ATMOSPHERIC GLOW OVERLAY */}
      <div className="from-primary/15 pointer-events-none absolute top-0 left-0 h-[500px] w-full bg-gradient-to-b to-transparent opacity-70 blur-3xl" />

      {/* Floating 3D Background Elements */}
      <div className="perspective-1000 absolute inset-0 overflow-hidden opacity-30">
        <motion.div
          className="from-primary/20 absolute top-1/4 left-1/6 h-32 w-32 transform-gpu rounded-full bg-gradient-to-r to-transparent blur-2xl"
          animate={{
            scale: [1, 1.3, 1],
            rotateY: [0, 180, 360],
            opacity: [0.2, 0.6, 0.2],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        />
        <motion.div
          className="from-secondary/20 absolute top-2/3 right-1/4 h-24 w-24 transform-gpu rounded-full bg-gradient-to-r to-transparent blur-xl"
          animate={{
            scale: [1, 0.7, 1],
            rotateX: [0, 180, 360],
            opacity: [0.1, 0.5, 0.1],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: 4,
          }}
        />
        <motion.div
          className="from-accent/30 absolute top-1/2 left-2/3 h-16 w-16 transform-gpu rounded-full bg-gradient-to-r to-transparent blur-lg"
          animate={{
            y: [0, -40, 0],
            rotateZ: [0, 360],
            opacity: [0.2, 0.7, 0.2],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: 2,
          }}
        />
      </div>

      {/* Header Section */}
      <div className="relative z-10 mx-auto mb-16 max-w-7xl px-4">
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, ease: 'easeOut' }}
        >
          <Trans
            i18nKey="clients.title"
            ns="common"
            parent={motion.h2}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1, delay: 0.2 }}
            className="from-primary via-secondary to-accent text-shadow-glow mb-6 bg-gradient-to-r bg-clip-text text-4xl font-bold text-transparent text-shadow-2xs md:text-6xl lg:text-7xl"
            components={{
              highlighted: <span className="font-extrabold italic" />,
            }}
          />

          <motion.p
            className="text-muted-foreground mx-auto max-w-2xl text-lg leading-relaxed font-semibold text-shadow-xs md:text-xl"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.4 }}
          >
            {t('clients.description')}
          </motion.p>

          <motion.div
            className="from-primary via-secondary to-accent shadow-primary/30 mx-auto mt-8 h-2 w-32 rounded-full bg-gradient-to-r shadow-lg"
            initial={{ scaleX: 0, opacity: 0 }}
            animate={{ scaleX: 1, opacity: 1 }}
            transition={{ duration: 1.2, delay: 0.6 }}
          />
        </motion.div>
      </div>

      {/* Infinite Carousel Container */}
      <div className="relative">
        <div
          className="flex transform-gpu items-center space-x-8 will-change-transform"
          style={{
            transform: `translateX(-${scrollX}px)`,
          }}
          ref={carouselRef}
          onMouseEnter={() => setIsPaused(true)}
          onMouseLeave={() => setIsPaused(false)}
        >
          {infiniteLogos.map((client, index) => (
            <motion.div
              key={`${client.name}-${index}`}
              className="group relative flex-shrink-0 transform-gpu"
              initial={{ opacity: 0, y: 60, rotateY: -30 }}
              animate={{ opacity: 1, y: 0, rotateY: 0 }}
              transition={{
                duration: 0.8,
                delay: (index % clientLogos.length) * 0.1,
                ease: 'easeOut',
              }}
              whileHover={{
                scale: 1.1,
                rotateY: 5,
                z: 50,
                transition: { duration: 0.3 },
              }}
            >
              {/* Glowing Background Effect */}
              <motion.div
                className="from-primary/10 via-secondary/10 to-accent/10 absolute -inset-4 transform-gpu rounded-2xl bg-gradient-to-r opacity-0 blur-xl group-hover:opacity-100"
                initial={false}
                animate={{
                  scale: isPaused ? [1, 1.05, 1] : 1,
                }}
                transition={{
                  duration: 2,
                  repeat: isPaused ? Infinity : 0,
                  ease: 'easeInOut',
                }}
              />

              {/* Card Container */}
              <motion.div
                className="from-card/80 to-card/60 dark:from-card/90 dark:to-card/70 border-border/30 group-hover:shadow-primary/20 preserve-3d relative h-40 w-64 transform-gpu rounded-2xl border bg-gradient-to-br shadow-lg backdrop-blur-sm group-hover:shadow-2xl"
                whileHover={{
                  boxShadow:
                    '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(59, 130, 246, 0.1)',
                }}
              >
                {/* Shimmer Effect */}
                <motion.div
                  className="absolute inset-0 transform-gpu rounded-2xl bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100"
                  animate={{
                    x: isPaused ? [-100, 300] : -100,
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: isPaused ? Infinity : 0,
                    ease: 'easeInOut',
                  }}
                />

                {/* Logo Container */}
                <div className="relative z-10 flex h-full transform-gpu items-center justify-center p-8">
                  <motion.div
                    className="relative h-16 w-32 transform-gpu"
                    whileHover={{
                      rotateY: 10,
                      rotateX: 5,
                    }}
                  >
                    <div className="flex h-full w-full items-center justify-center rounded-lg transition-all duration-300">
                      <Image
                        src={client.src}
                        alt={client.alt}
                        fill
                        className="object-contain filter transition-all duration-300"
                        sizes="(max-width: 768px) 64px, 80px"
                      />
                    </div>

                    {/* 3D Reflection Effect */}
                    <motion.div
                      className="absolute inset-0 transform-gpu rounded-lg"
                      animate={{
                        opacity: [0.3, 0.6, 0.3],
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        ease: 'easeInOut',
                      }}
                    />
                  </motion.div>
                </div>

                {/* Corner Accents */}
                <div className="from-primary to-secondary absolute top-3 left-3 h-2 w-2 rounded-full bg-gradient-to-r opacity-60" />
                <div className="from-secondary to-accent absolute right-3 bottom-3 h-2 w-2 rounded-full bg-gradient-to-r opacity-60" />
              </motion.div>

              {/* Floating Particles */}
              <motion.div
                className="bg-accent absolute -top-2 -right-2 h-1 w-1 transform-gpu rounded-full opacity-0 group-hover:opacity-100"
                animate={{
                  y: [0, -20, 0],
                  opacity: isPaused ? [0, 1, 0] : 0,
                }}
                transition={{
                  duration: 2,
                  repeat: isPaused ? Infinity : 0,
                  ease: 'easeInOut',
                }}
              />
            </motion.div>
          ))}
        </div>

        {/* Gradient Fade Edges */}
        <div className="from-background pointer-events-none absolute top-0 left-0 z-20 h-full w-32 bg-gradient-to-r to-transparent" />
        <div className="from-background pointer-events-none absolute top-0 right-0 z-20 h-full w-32 bg-gradient-to-l to-transparent" />
      </div>

      {/* Subtle Grid Pattern Overlay */}
      <div className="pointer-events-none absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,oklch(0.5_0.05_240)_1px,transparent_0)] [background-size:40px_40px] opacity-[0.02] dark:opacity-[0.03]" />
    </section>
  );
};

export default InfiniteClientsCarousel;
