'use client';

import React from 'react';
import { motion } from 'motion/react';
import { useTranslation } from 'react-i18next';
import { ArrowRight, Mail, MessageCircle, Send } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

const ContactCTA = () => {
  const { t } = useTranslation('careers');

  return (
    <section className="relative px-4 py-20 lg:py-32">
      {/* Background Elements */}
      <div className="absolute inset-0 -z-10">
        <div className="from-primary/10 via-secondary/10 to-accent/10 absolute inset-0 bg-gradient-to-br" />
        <div className="from-primary/20 to-secondary/20 absolute top-1/4 left-1/4 h-96 w-96 rounded-full bg-gradient-to-r blur-3xl" />
        <div className="from-secondary/20 to-accent/20 absolute right-1/4 bottom-1/4 h-96 w-96 rounded-full bg-gradient-to-r blur-3xl" />
      </div>

      <div className="container mx-auto max-w-4xl">
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true, margin: '-100px' }}
          transition={{
            staggerChildren: 0.1,
            delayChildren: 0.2,
          }}
          className="space-y-12"
        >
          {/* Main CTA Card */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{
              duration: 0.6,
              ease: [0.25, 0.46, 0.45, 0.94],
            }}
          >
            <Card className="from-card/90 to-card/70 border-primary/30 relative overflow-hidden bg-gradient-to-br backdrop-blur-sm">
              {/* Decorative Elements */}
              <div className="from-primary/20 to-secondary/20 absolute top-0 right-0 h-32 w-32 rounded-full bg-gradient-to-br blur-2xl" />
              <div className="from-secondary/20 to-accent/20 absolute bottom-0 left-0 h-32 w-32 rounded-full bg-gradient-to-br blur-2xl" />

              <CardContent className="relative space-y-8 p-8 text-center lg:p-12">
                {/* Icon */}
                <motion.div
                  className="flex justify-center"
                  initial={{ scale: 0 }}
                  whileInView={{ scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ delay: 0.3, duration: 0.5, type: 'spring' }}
                >
                  <div className="bg-primary/20 flex h-20 w-20 items-center justify-center rounded-full">
                    <div className="from-primary to-secondary flex h-14 w-14 items-center justify-center rounded-full bg-gradient-to-r shadow-lg">
                      <MessageCircle className="h-7 w-7 text-white" />
                    </div>
                  </div>
                </motion.div>

                {/* Content */}
                <div className="space-y-6">
                  <motion.h2
                    className="text-foreground text-3xl font-bold md:text-4xl lg:text-5xl"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ delay: 0.4, duration: 0.6 }}
                  >
                    {t('contact.title')}
                  </motion.h2>

                  <motion.p
                    className="text-muted-foreground mx-auto max-w-2xl text-xl leading-relaxed"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ delay: 0.5, duration: 0.6 }}
                  >
                    {t('contact.subtitle')}
                  </motion.p>

                  <motion.p
                    className="text-muted-foreground mx-auto max-w-3xl leading-relaxed"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ delay: 0.6, duration: 0.6 }}
                  >
                    {t('contact.description')}
                  </motion.p>
                </div>

                {/* CTA Buttons */}
                <motion.div
                  className="flex flex-col gap-4 sm:flex-row sm:justify-center"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: 0.7, duration: 0.6 }}
                >
                  {/* Primary CTA */}
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Button
                      size="lg"
                      className="from-primary to-secondary hover:from-primary/90 hover:to-secondary/90 text-primary-foreground bg-gradient-to-r shadow-lg transition-all duration-300 hover:shadow-xl"
                      asChild
                    >
                      <a
                        href={`mailto:${t('contact.cta.email')}?subject=Career Inquiry`}
                      >
                        {t('contact.cta.apply')}
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </a>
                    </Button>
                  </motion.div>

                  {/* Secondary CTA */}
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Button
                      variant="outline"
                      size="lg"
                      className="border-border/50 hover:border-primary/50 hover:bg-primary/5 transition-all duration-300"
                      asChild
                    >
                      <a
                        href={`mailto:${t('contact.cta.email')}?subject=General Career Inquiry`}
                      >
                        <Mail className="mr-2 h-4 w-4" />
                        {t('contact.cta.contact')}
                      </a>
                    </Button>
                  </motion.div>
                </motion.div>

                {/* Contact Info */}
                <motion.div
                  className="border-border/30 space-y-4 border-t pt-8"
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  viewport={{ once: true }}
                  transition={{ delay: 0.8, duration: 0.6 }}
                >
                  <div className="flex items-center justify-center gap-2">
                    <span className="text-muted-foreground">
                      {t('contact.or')}
                    </span>
                  </div>

                  <motion.div
                    className="flex items-center justify-center gap-2"
                    whileHover={{ scale: 1.02 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Send className="text-primary h-4 w-4" />
                    <a
                      href={`mailto:${t('contact.cta.email')}`}
                      className="text-primary hover:text-primary/80 font-medium transition-colors duration-200"
                    >
                      {t('contact.cta.email')}
                    </a>
                  </motion.div>

                  <p className="text-muted-foreground text-sm">
                    {t('contact.general_inquiry')}
                  </p>
                </motion.div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Additional Info Cards */}
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{
              staggerChildren: 0.1,
              delayChildren: 0.2,
            }}
            className="grid gap-6 md:grid-cols-2"
          >
            {/* Quick Apply Card */}
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{
                duration: 0.6,
                ease: [0.25, 0.46, 0.45, 0.94],
                delay: 0.1,
              }}
            >
              <Card className="bg-card/80 border-border/50 group hover:border-primary/30 h-full backdrop-blur-sm transition-all duration-300 hover:shadow-lg">
                <CardContent className="space-y-4 p-6 text-center">
                  <div className="bg-primary/10 mx-auto flex h-12 w-12 items-center justify-center rounded-full">
                    <ArrowRight className="text-primary h-6 w-6" />
                  </div>
                  <h3 className="text-foreground text-lg font-semibold">
                    Quick Application
                  </h3>
                  <p className="text-muted-foreground text-sm leading-relaxed">
                    Send us your resume and we&apos;ll match you with suitable
                    positions.
                  </p>
                </CardContent>
              </Card>
            </motion.div>

            {/* General Inquiry Card */}
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{
                duration: 0.6,
                ease: [0.25, 0.46, 0.45, 0.94],
                delay: 0.2,
              }}
            >
              <Card className="bg-card/80 border-border/50 group hover:border-primary/30 h-full backdrop-blur-sm transition-all duration-300 hover:shadow-lg">
                <CardContent className="space-y-4 p-6 text-center">
                  <div className="bg-secondary/10 mx-auto flex h-12 w-12 items-center justify-center rounded-full">
                    <MessageCircle className="text-secondary h-6 w-6" />
                  </div>
                  <h3 className="text-foreground text-lg font-semibold">
                    Have Questions?
                  </h3>
                  <p className="text-muted-foreground text-sm leading-relaxed">
                    Reach out to our HR team for any career-related questions.
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default ContactCTA;
