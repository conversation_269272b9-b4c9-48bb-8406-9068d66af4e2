'use client';

import React from 'react';
import Link from 'next/link';
import { useTranslation, Trans } from 'react-i18next';
import { motion } from 'motion/react';
import { productData } from '@/constants/products';
import GradientHighlighter from '@/components/gradient-highlighter';

const Products = () => {
  const { t } = useTranslation('products');

  return (
    <section className="from-background via-muted/30 to-background relative overflow-hidden bg-gradient-to-br py-24">
      <div className="absolute inset-0 opacity-30 dark:opacity-20">
        <div className="from-primary/20 to-secondary/20 absolute top-1/4 -left-48 h-96 w-96 animate-pulse rounded-full bg-gradient-to-r blur-3xl" />
        <div
          className="from-secondary/20 to-accent/20 absolute -right-48 bottom-1/4 h-96 w-96 animate-pulse rounded-full bg-gradient-to-r blur-3xl"
          style={{ animationDelay: '2s' }}
        />
      </div>

      <div className="relative z-10 container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: '-100px' }}
          transition={{ duration: 0.8, ease: 'easeOut' }}
          className="mb-16 text-center"
        >
          <Trans
            i18nKey="header.title"
            ns="products"
            parent={'h2'}
            className="from-foreground to-muted-foreground mb-6 bg-gradient-to-r bg-clip-text text-4xl leading-[1.15] font-bold text-transparent text-shadow-2xs md:text-5xl lg:text-6xl"
            components={{
              highlighted1: <GradientHighlighter />,
              highlighted2: <GradientHighlighter />,
              br: <br />,
            }}
          />
          <p className="text-shadow-xstext-muted-foreground mx-auto max-w-4xl text-xl leading-relaxed">
            {t('header.description')}
          </p>
        </motion.div>

        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {productData.map((product) => {
            const IconComponent = product.icon;
            // The key for translation will be, e.g., "modules.received-invoices.title"
            const translationKey = `modules.${product.id}`;

            return (
              <Link
                key={product.id}
                href={`/products/${product.id}`}
                className="group relative h-full"
                aria-label={t(`${translationKey}.title`)}
              >
                <motion.div
                  initial={{ opacity: 0, y: 50, scale: 0.9 }}
                  whileInView={{ opacity: 1, y: 0, scale: 1 }}
                  viewport={{ once: true, margin: '-50px' }}
                  transition={{
                    duration: 0.6,
                    delay: product.delay,
                    ease: 'easeOut',
                  }}
                  whileHover={{
                    y: -8,
                    scale: 1.02,
                    rotateY: 5,
                    transition: { duration: 0.3, ease: 'easeOut' },
                  }}
                  className="h-full"
                >
                  <div className="bg-card/50 border-border/50 hover:border-primary/50 hover:shadow-primary/10 perspective-1000 relative h-full transform-gpu rounded-2xl border p-6 backdrop-blur-sm transition-all duration-300 hover:shadow-2xl">
                    <div
                      className={`relative mb-6 h-16 w-16 rounded-xl bg-gradient-to-br ${product.gradient} p-3 shadow-lg transition-transform duration-300 group-hover:scale-110`}
                    >
                      <IconComponent className="h-full w-full text-white" />
                      <div className="absolute inset-0 rounded-xl bg-white/20 opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
                    </div>

                    <div className="flex h-[calc(100%-88px)] flex-col space-y-4">
                      <h3 className="text-foreground group-hover:text-primary text-xl font-semibold transition-colors duration-300">
                        {t(`${translationKey}.title`)}
                      </h3>
                      <p className="text-muted-foreground flex-grow text-sm leading-relaxed">
                        {t(`${translationKey}.description`)}
                      </p>
                      <div className="pt-2">
                        <span className="text-primary inline-flex items-center text-sm font-medium transition-all duration-300 group-hover:gap-2">
                          {t('card.learnMore')}
                          <motion.span
                            className="ml-1"
                            animate={{ x: [0, 4, 0] }}
                            transition={{
                              duration: 1.5,
                              repeat: Infinity,
                              ease: 'easeInOut',
                            }}
                          >
                            →
                          </motion.span>
                        </span>
                      </div>
                    </div>

                    <div className="from-primary/5 to-secondary/5 pointer-events-none absolute inset-0 rounded-2xl bg-gradient-to-br opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
                    <div className="pointer-events-none absolute inset-0 rounded-2xl opacity-0 transition-opacity duration-500 group-hover:opacity-100">
                      <div
                        className={`absolute inset-0 rounded-2xl bg-gradient-to-br ${product.gradient} scale-110 opacity-10 blur-xl`}
                      />
                    </div>
                  </div>
                </motion.div>
              </Link>
            );
          })}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: '-100px' }}
          transition={{ duration: 0.8, delay: 0.5 }}
          className="mt-16 text-center"
        >
          <div className="inline-flex flex-col gap-4 sm:flex-row">
            <motion.button
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
              className="from-primary to-secondary text-primary-foreground transform-gpu rounded-xl bg-gradient-to-r px-8 py-4 font-semibold shadow-xl transition-all duration-300 hover:shadow-2xl"
            >
              {t('buttons.explore')}
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
              className="border-primary text-primary hover:bg-primary hover:text-primary-foreground transform-gpu rounded-xl border-2 px-8 py-4 font-semibold transition-all duration-300"
            >
              {t('buttons.demo')}
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Products;
